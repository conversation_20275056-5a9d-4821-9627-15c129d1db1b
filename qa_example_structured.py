#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
QA Library Example - Structured Task-Based Usage
"""

import os
import sys
from pathlib import Path
from datetime import datetime
from qa import create_qa_engine, quick_qa, batch_qa

class QADemo:
    """QA Library demonstration with structured tasks"""
    
    def __init__(self, collection_name: str = "demo_collection"):
        self.collection_name = collection_name
        self.qa_engine = None
        self.indexed_files = []
        
    def setup_engine(self):
        """Task 1: Setup QA Engine"""
        print("🚀 Task 1: Setting up QA Engine")
        print("-" * 40)
        
        try:
            self.qa_engine = create_qa_engine(self.collection_name)
            print(f"✅ QA Engine created with collection: {self.collection_name}")
            
            # Get initial stats
            stats = self.qa_engine.get_collection_stats()
            print(f"📊 Collection stats: {stats}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to setup QA Engine: {e}")
            return False
    
    def index_documents(self, documents_path: str = None):
        """Task 2: Index Documents"""
        print(f"\n📚 Task 2: Indexing Documents")
        print("-" * 40)
        
        if not self.qa_engine:
            print("❌ QA Engine not initialized. Run setup_engine() first.")
            return False
        
        indexed_count = 0
        
        # Index from specified path or current directory
        if documents_path and os.path.exists(documents_path):
            if os.path.isdir(documents_path):
                print(f"📁 Indexing directory: {documents_path}")
                indexed_count = self.qa_engine.index_directory(documents_path)
            else:
                print(f"📄 Indexing file: {documents_path}")
                if self.qa_engine.index_document_file(documents_path):
                    indexed_count = 1
                    self.indexed_files.append(documents_path)
        else:
            # Index files in current directory
            print("📁 Indexing files in current directory...")
            
            # Find supported files
            docx_files = list(Path('.').glob('*.docx'))
            txt_files = list(Path('.').glob('*.txt'))
            
            # Index DOCX files
            if docx_files:
                print(f"Found {len(docx_files)} DOCX files")
                for file_path in docx_files[:5]:  # Limit to first 5
                    print(f"  📄 Indexing: {file_path}")
                    if self.qa_engine.index_document_file(str(file_path)):
                        indexed_count += 1
                        self.indexed_files.append(str(file_path))
            
            # Index TXT files
            if txt_files:
                print(f"Found {len(txt_files)} TXT files")
                for file_path in txt_files[:3]:  # Limit to first 3
                    print(f"  📄 Indexing: {file_path}")
                    if self.qa_engine.index_document_file(str(file_path)):
                        indexed_count += 1
                        self.indexed_files.append(str(file_path))
            
            # Index texts directory if exists
            if Path('texts').exists():
                print("📁 Indexing 'texts' directory...")
                dir_count = self.qa_engine.index_directory('texts')
                indexed_count += dir_count
        
        print(f"\n✅ Successfully indexed {indexed_count} documents")
        print(f"📋 Total indexed files: {len(self.indexed_files)}")
        
        # Show updated stats
        stats = self.qa_engine.get_collection_stats()
        print(f"📊 Updated collection stats: {stats}")
        
        return indexed_count > 0
    
    def search_documents(self, query: str, num_results: int = 3):
        """Task 3: Search Documents"""
        print(f"\n🔍 Task 3: Searching Documents")
        print("-" * 40)
        
        if not self.qa_engine:
            print("❌ QA Engine not initialized. Run setup_engine() first.")
            return []
        
        print(f"🔍 Query: {query}")
        print(f"🎯 Searching for top {num_results} results...")
        
        try:
            results = self.qa_engine.search_documents(query, num_results)
            
            if not results:
                print("❌ No documents found matching the query")
                return []
            
            print(f"✅ Found {len(results)} relevant documents:")
            
            for i, result in enumerate(results, 1):
                print(f"\n📄 Result {i}:")
                print(f"  📋 Title: {result.document.title}")
                print(f"  📊 Score: {result.score:.2f}")
                print(f"  📝 Content preview: {result.document.content[:150]}...")
                
                if result.highlights:
                    print(f"  🔍 Highlights:")
                    for highlight in result.highlights[:2]:
                        print(f"    • {highlight}")
            
            return results
            
        except Exception as e:
            print(f"❌ Search failed: {e}")
            return []
    
    def ask_questions(self, questions: list = None):
        """Task 4: Ask Questions"""
        print(f"\n❓ Task 4: Asking Questions")
        print("-" * 40)
        
        if not self.qa_engine:
            print("❌ QA Engine not initialized. Run setup_engine() first.")
            return
        
        # Use provided questions or default ones
        if questions is None:
            questions = [
                "Tài liệu này nói về gì?",
                "Mục đích chính của hoạt động là gì?",
                "Có những bước chuẩn bị nào?",
                "Tiến hành hoạt động như thế nào?",
                "Ngày thực hiện là khi nào?"
            ]
        
        print(f"🎯 Asking {len(questions)} questions...")
        
        for i, question in enumerate(questions, 1):
            print(f"\n❓ Question {i}: {question}")
            print("-" * 30)
            
            try:
                response = self.qa_engine.ask_question(question, num_context_docs=3)
                
                print(f"💬 Answer: {response.answer}")
                print(f"🎯 Confidence: {response.confidence:.2f}")
                print(f"📚 Sources: {len(response.sources)} documents")
                print(f"🤖 Model: {response.model_used}")
                print(f"🔢 Tokens: {response.tokens_used}")
                
                # Show top sources
                if response.sources:
                    print("📖 Top sources:")
                    for j, source in enumerate(response.sources[:2], 1):
                        print(f"  {j}. {source.document.title} (score: {source.score:.2f})")
                
            except Exception as e:
                print(f"❌ Error asking question: {e}")
    
    def batch_processing(self, questions: list = None):
        """Task 5: Batch Processing"""
        print(f"\n⚡ Task 5: Batch Processing")
        print("-" * 40)
        
        if questions is None:
            questions = [
                "Tóm tắt nội dung chính của tài liệu",
                "Liệt kê các bước thực hiện",
                "Ai là người thực hiện?",
                "Kết quả mong đợi là gì?"
            ]
        
        print(f"⚡ Processing {len(questions)} questions in batch...")
        
        try:
            responses = batch_qa(questions, collection_name=self.collection_name)
            
            print(f"✅ Batch processing completed!")
            print(f"📊 Results summary:")
            
            for i, response in enumerate(responses, 1):
                print(f"\n📝 Batch Result {i}:")
                print(f"  ❓ Q: {response.question}")
                print(f"  💬 A: {response.answer[:100]}...")
                print(f"  🎯 Confidence: {response.confidence:.2f}")
                print(f"  📚 Sources: {len(response.sources)}")
            
            return responses
            
        except Exception as e:
            print(f"❌ Batch processing failed: {e}")
            return []
    
    def conversation_demo(self):
        """Task 6: Conversation Demo"""
        print(f"\n💬 Task 6: Conversation Demo")
        print("-" * 40)
        
        if not self.qa_engine:
            print("❌ QA Engine not initialized. Run setup_engine() first.")
            return
        
        conversation_questions = [
            "Tài liệu này về chủ đề gì?",
            "Bạn có thể nói rõ hơn về mục đích không?",
            "Còn về cách thực hiện thì sao?",
            "Có gì khác cần lưu ý không?"
        ]
        
        print("🎭 Starting conversation demo...")
        print("💡 Note: Each question will reference previous context")
        
        for i, question in enumerate(conversation_questions, 1):
            print(f"\n💬 Turn {i}: {question}")
            print("-" * 25)
            
            try:
                response = self.qa_engine.ask_question(
                    question, 
                    use_conversation_history=True
                )
                
                print(f"🤖 Response: {response.answer}")
                print(f"🎯 Confidence: {response.confidence:.2f}")
                
            except Exception as e:
                print(f"❌ Error in conversation: {e}")
        
        # Show conversation history
        print(f"\n📚 Conversation History:")
        for i, entry in enumerate(self.qa_engine.conversation_history, 1):
            print(f"  {i}. Q: {entry['question']}")
            print(f"     A: {entry['answer'][:50]}...")
    
    def export_results(self):
        """Task 7: Export Results"""
        print(f"\n💾 Task 7: Export Results")
        print("-" * 40)
        
        if not self.qa_engine:
            print("❌ QA Engine not initialized. Run setup_engine() first.")
            return
        
        try:
            # Export conversation history
            history_file = f"conversation_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            self.qa_engine.export_conversation_history(history_file)
            print(f"✅ Conversation history exported to: {history_file}")
            
            # Export collection stats
            stats_file = f"collection_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            stats = self.qa_engine.get_collection_stats()
            
            import json
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Collection stats exported to: {stats_file}")
            
            # Create summary report
            summary_file = f"qa_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("QA Library Demo Summary\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Collection: {self.collection_name}\n")
                f.write(f"Indexed Files: {len(self.indexed_files)}\n")
                f.write(f"Total Documents: {stats.get('num_documents', 0)}\n")
                f.write(f"Demo Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write("Indexed Files:\n")
                for file_path in self.indexed_files:
                    f.write(f"  - {file_path}\n")
                
                f.write(f"\nConversation History: {len(self.qa_engine.conversation_history)} entries\n")
            
            print(f"✅ Summary report exported to: {summary_file}")
            
        except Exception as e:
            print(f"❌ Export failed: {e}")
    
    def cleanup(self):
        """Task 8: Cleanup"""
        print(f"\n🧹 Task 8: Cleanup")
        print("-" * 40)
        
        if self.qa_engine:
            self.qa_engine.clear_conversation_history()
            print("✅ Conversation history cleared")
        
        print("✅ Cleanup completed")

def main():
    """Main demo function"""
    print("🚀 QA Library with Typesense - Structured Demo")
    print("=" * 60)
    
    # Initialize demo
    demo = QADemo("structured_demo")
    
    try:
        # Task 1: Setup
        if not demo.setup_engine():
            print("❌ Setup failed. Exiting.")
            return
        
        # Task 2: Index documents
        if not demo.index_documents():
            print("⚠️  No documents indexed. Proceeding with limited functionality.")
        
        # Task 3: Search demonstration
        demo.search_documents("mục đích hoạt động", 3)
        
        # Task 4: Q&A demonstration
        demo.ask_questions()
        
        # Task 5: Batch processing
        demo.batch_processing()
        
        # Task 6: Conversation demo
        demo.conversation_demo()
        
        # Task 7: Export results
        demo.export_results()
        
        # Task 8: Cleanup
        demo.cleanup()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
    finally:
        print("\n" + "=" * 60)
        print("✅ QA Library Demo Completed!")
        print("\nKey Features Demonstrated:")
        print("• Document indexing and processing")
        print("• Intelligent search with relevance scoring")
        print("• Context-aware question answering")
        print("• Conversation history management")
        print("• Batch processing capabilities")
        print("• Results export and reporting")
        print("\nFor more advanced usage, check the documentation!")

if __name__ == "__main__":
    main()
