# Migration Guide: TypesenseVectorDB → TypesenseVectorStore

This guide helps you migrate from the legacy `TypesenseVectorDB` class to the new `TypesenseVectorStore` implementation that follows the PHP structure.

## Overview

The new `TypesenseVectorStore` provides:
- ✅ Clean separation of concerns
- ✅ Better type safety with Document objects
- ✅ Interface-based design
- ✅ Vector dimension validation
- ✅ Batch processing support
- ✅ Consistent field naming with PHP implementation

## Quick Comparison

### Legacy Approach (TypesenseVectorDB)
```python
# Old way - monolithic class
from libs.typesense_vector_db import TypesenseVectorDB

db = TypesenseVectorDB(collection_name="my_collection")
# Everything is handled internally
```

### New Approach (TypesenseVectorStore)
```python
# New way - clean separation
from libs.typesense_vector_db import (
    create_typesense_vector_store, 
    Document
)
from libs.custom_embedding import CustomEmbedding

# Create vector store
vector_store = create_typesense_vector_store("my_collection", 384)

# Create embedding service separately
embedding_service = CustomEmbedding(model, api_base, api_key)
```

## Step-by-Step Migration

### 1. Basic Setup

**Before (Legacy):**
```python
db = TypesenseVectorDB(collection_name="documents")
```

**After (New):**
```python
vector_store = create_typesense_vector_store("documents", 384)
embedding_service = create_embedding_service()  # Your helper function
```

### 2. Adding Documents

**Before (Legacy):**
```python
# Legacy used internal methods with complex parameters
result = db.import_docx_to_typesense(file_path, title, metadata)
```

**After (New):**
```python
# Create Document objects
doc = Document("Your content here")
doc.source_type = "docx"
doc.source_name = "document.docx"
doc.hash = "unique_hash"
doc.chunk_number = 0
doc.embedding = embedding_service.embed_query(doc.content)

# Add to vector store
vector_store.add_document(doc)

# Or add multiple documents
vector_store.add_documents([doc1, doc2, doc3])
```

### 3. Similarity Search

**Before (Legacy):**
```python
result = db.search_similar_documents(query, limit=5, threshold=0.7)
documents = result["documents"]
```

**After (New):**
```python
# Generate query embedding
query_embedding = embedding_service.embed_query(query)

# Search similar documents
documents = vector_store.similarity_search(
    embedding=query_embedding,
    k=5,
    additional_arguments={"filter": "sourceType:docx"}
)
```

## Field Mapping

The new implementation uses consistent field names with the PHP version:

| Legacy Field | New Field | Description |
|-------------|-----------|-------------|
| `source_file` | `sourceName` | Source file name |
| `chunk_index` | `chunkNumber` | Chunk index |
| `metadata_json` | N/A | Use Document properties |
| `content_hash` | `hash` | Document hash |

## Migration Strategies

### Strategy 1: Gradual Migration
Keep both implementations running side by side:

```python
# Use legacy for existing features
legacy_db = TypesenseVectorDB("legacy_collection")

# Use new implementation for new features  
new_store = create_typesense_vector_store("new_collection", 384)
```

### Strategy 2: Wrapper Approach
Create a wrapper that uses the new implementation internally:

```python
class MigratedVectorDB:
    def __init__(self, collection_name):
        self.vector_store = create_typesense_vector_store(collection_name, 384)
        self.embedding_service = create_embedding_service()
    
    def search_similar_documents(self, query, limit=5, threshold=0.7):
        query_embedding = self.embedding_service.embed_query(query)
        results = self.vector_store.similarity_search(query_embedding, k=limit)
        # Convert to legacy format if needed
        return self._convert_to_legacy_format(results)
```

### Strategy 3: Complete Migration
Replace all legacy code with new implementation:

1. Update imports
2. Replace initialization code
3. Convert document creation logic
4. Update search logic
5. Test thoroughly

## Common Migration Issues

### Issue 1: Vector Dimension Mismatch
**Problem:** Different embedding dimensions between old and new collections.

**Solution:**
```python
# Check existing collection dimension first
try:
    vector_store = create_typesense_vector_store("existing_collection", 384)
except Exception as e:
    if "dimension" in str(e):
        # Use correct dimension or create new collection
        vector_store = create_typesense_vector_store("new_collection", 1536)
```

### Issue 2: Field Name Changes
**Problem:** Queries using old field names fail.

**Solution:**
```python
# Update filter queries
# Old: filter_by='source_file:document.pdf'
# New: filter_by='sourceName:document.pdf'

additional_arguments = {
    "filter": "sourceName:document.pdf AND sourceType:pdf"
}
```

### Issue 3: Embedding Service Separation
**Problem:** Embeddings were handled internally in legacy version.

**Solution:**
```python
# Create reusable embedding service
def create_embedding_service():
    return CustomEmbedding(
        model=get_env_var('EMBEDDINGS_MODEL'),
        api_base=get_env_var('EMBEDDINGS_API_BASE'),
        api_key=get_env_var('EMBEDDINGS_API_KEY')
    )

# Use throughout your application
embedding_service = create_embedding_service()
```

## Testing Your Migration

### 1. Unit Tests
```python
def test_document_creation():
    doc = Document("test content")
    doc.source_type = "test"
    doc.source_name = "test.txt"
    doc.hash = "test_hash"
    doc.chunk_number = 0
    
    assert doc.content == "test content"
    assert doc.source_type == "test"

def test_vector_store_operations():
    vector_store = create_typesense_vector_store("test_collection", 384)
    # Add test documents and verify operations
```

### 2. Integration Tests
```python
def test_end_to_end_workflow():
    # Create vector store
    # Add documents
    # Perform searches
    # Verify results match expected format
```

## Best Practices

1. **Always validate vector dimensions** before creating collections
2. **Use batch operations** for better performance with `add_documents()`
3. **Separate embedding logic** from vector store operations
4. **Handle exceptions properly** - new implementation has better error messages
5. **Use factory functions** for consistent setup
6. **Test with small datasets** before migrating large collections

## Rollback Plan

If you need to rollback to the legacy implementation:

1. Keep legacy code in a separate branch
2. Use feature flags to switch between implementations
3. Maintain data compatibility between both versions
4. Have monitoring in place to detect issues quickly

## Support

- Check `example_new_vector_store.py` for working examples
- Run `qa_test.py` and choose option 1 for new implementation testing
- Review the PHP implementation for reference
- Legacy implementation remains available for backward compatibility

## Timeline Recommendation

- **Week 1-2:** Set up new implementation alongside legacy
- **Week 3-4:** Migrate non-critical features
- **Week 5-6:** Migrate critical features with thorough testing
- **Week 7-8:** Monitor and optimize performance
- **Week 9+:** Gradually deprecate legacy implementation
