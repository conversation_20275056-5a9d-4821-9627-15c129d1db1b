#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra TypesenseVectorDB với custom embeddings
"""

import os
import sys
import tempfile
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_typesense_with_custom_embeddings():
    """Test TypesenseVectorDB with custom embeddings"""
    
    try:
        # Import with proper path handling
        from libs.typesense_vector_db import TypesenseVectorDB
        
        print("🚀 Initializing TypesenseVectorDB with custom embeddings...")
        
        # Initialize the vector database
        vector_db = TypesenseVectorDB(collection_name="test_embeddings")
        
        print("✅ TypesenseVectorDB initialized successfully!")
        
        # Test embedding creation directly
        print("\n🧪 Testing direct embedding creation...")
        test_text = "This is a test document for embedding."
        
        embedding = vector_db.embeddings.embed_query(test_text)
        print(f"✅ Direct embedding created successfully!")
        print(f"   Dimension: {len(embedding)}")
        print(f"   First 5 values: {embedding[:5]}")
        
        # Test search functionality
        print("\n🔍 Testing search functionality...")
        search_result = vector_db.search_similar_documents(
            query="test document",
            limit=5,
            threshold=0.5
        )
        
        print(f"✅ Search completed!")
        print(f"   Success: {search_result.get('success', False)}")
        print(f"   Method: {search_result.get('search_method', 'unknown')}")
        print(f"   Found: {search_result.get('total_found', 0)} documents")
        
        # Test with a simple document import (create a temporary text file)
        print("\n📄 Testing document import...")
        
        # Create a temporary docx-like content for testing
        test_content = """
        Đây là một tài liệu test để kiểm tra chức năng embedding.
        
        Chức năng chính:
        - Tạo embedding từ văn bản
        - Lưu trữ trong Typesense
        - Tìm kiếm tương tự
        
        Kết luận: Hệ thống hoạt động tốt với custom embeddings.
        """
        
        # Test chunking
        chunks = vector_db.chunk_text(test_content, chunk_size=100, overlap=20)
        print(f"✅ Text chunking successful!")
        print(f"   Number of chunks: {len(chunks)}")
        for i, chunk in enumerate(chunks):
            print(f"   Chunk {i+1}: {chunk[:50]}...")
        
        # Test embedding for chunks
        print("\n🔄 Testing embedding creation for chunks...")
        for i, chunk in enumerate(chunks[:2]):  # Test only first 2 chunks
            try:
                chunk_embedding = vector_db.embeddings.embed_query(chunk)
                print(f"✅ Chunk {i+1} embedding created (dimension: {len(chunk_embedding)})")
            except Exception as e:
                print(f"❌ Error creating embedding for chunk {i+1}: {e}")
                return False
        
        print("\n🎉 All tests passed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed and paths are correct")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_variables():
    """Test if all required environment variables are set"""

    print("🔧 Checking environment variables...")

    # Import get_env_var function
    from libs.common import get_env_var

    required_vars = [
        'EMBEDDINGS_MODEL',
        'EMBEDDINGS_API_BASE',
        'EMBEDDINGS_API_KEY',
        'TYPESENSE_HOST',
        'TYPESENSE_PORT',
        'TYPESENSE_API_KEY'
    ]

    missing_vars = []
    for var in required_vars:
        value = get_env_var(var)
        if value:
            if 'KEY' in var:
                print(f"   ✅ {var}: ***{value[-4:]}")
            else:
                print(f"   ✅ {var}: {value}")
        else:
            missing_vars.append(var)
            print(f"   ❌ {var}: Not set")

    if missing_vars:
        print(f"\n⚠️  Missing environment variables: {missing_vars}")
        return False
    else:
        print(f"\n✅ All environment variables are set!")
        return True

if __name__ == "__main__":
    print("🚀 Starting TypesenseVectorDB embedding test...")
    
    # Check environment first
    env_ok = test_environment_variables()
    if not env_ok:
        print("\n💥 Environment check failed!")
        sys.exit(1)
    
    # Run main test
    success = test_typesense_with_custom_embeddings()
    
    if success:
        print("\n🎉 All tests completed successfully!")
        print("✅ Your embed_query() function should now work correctly!")
    else:
        print("\n💥 Tests failed!")
        sys.exit(1)
