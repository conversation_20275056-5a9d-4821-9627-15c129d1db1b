#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
QA Library Utilities - Task-Specific Functions
"""

import os
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
from qa import create_qa_engine, quick_qa, batch_qa

class QAUtility:
    """Utility class for common QA tasks"""
    
    def __init__(self, collection_name: str = "qa_utility"):
        self.collection_name = collection_name
        self.qa_engine = create_qa_engine(collection_name)
    
    def import_collection(self, documents_path: str, file_extensions: List[str] = None) -> Dict:
        """
        Import documents into collection
        
        Args:
            documents_path: Path to documents (file or directory)
            file_extensions: List of file extensions to process
            
        Returns:
            Dict with import results
        """
        if file_extensions is None:
            file_extensions = ['.docx', '.txt']
        
        results = {
            'success': False,
            'indexed_files': 0,
            'failed_files': 0,
            'total_chunks': 0,
            'errors': []
        }
        
        try:
            if not os.path.exists(documents_path):
                results['errors'].append(f"Path not found: {documents_path}")
                return results
            
            if os.path.isfile(documents_path):
                # Import single file
                success = self.qa_engine.index_document_file(documents_path)
                if success:
                    results['indexed_files'] = 1
                    results['success'] = True
                else:
                    results['failed_files'] = 1
                    results['errors'].append(f"Failed to index: {documents_path}")
            
            elif os.path.isdir(documents_path):
                # Import directory
                indexed_count = self.qa_engine.index_directory(documents_path, file_extensions)
                results['indexed_files'] = indexed_count
                results['success'] = indexed_count > 0
                
                # Count total files for comparison
                total_files = 0
                for root, dirs, files in os.walk(documents_path):
                    for file in files:
                        if any(file.lower().endswith(ext) for ext in file_extensions):
                            total_files += 1
                
                results['failed_files'] = total_files - indexed_count
            
            # Get collection stats
            stats = self.qa_engine.get_collection_stats()
            results['total_chunks'] = stats.get('num_documents', 0)
            
            return results
            
        except Exception as e:
            results['errors'].append(f"Import error: {str(e)}")
            return results
    
    def ask_single_question(self, question: str, num_context_docs: int = 3) -> Dict:
        """
        Ask a single question and return structured response
        
        Args:
            question: The question to ask
            num_context_docs: Number of context documents to use
            
        Returns:
            Dict with question response
        """
        try:
            response = self.qa_engine.ask_question(question, num_context_docs)
            
            return {
                'success': True,
                'question': response.question,
                'answer': response.answer,
                'confidence': response.confidence,
                'sources': [
                    {
                        'title': source.document.title,
                        'score': source.score,
                        'highlights': source.highlights,
                        'content_preview': source.document.content[:200] + "..."
                    }
                    for source in response.sources
                ],
                'model_used': response.model_used,
                'tokens_used': response.tokens_used,
                'timestamp': response.timestamp.isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'question': question,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def ask_multiple_questions(self, questions: List[str]) -> List[Dict]:
        """
        Ask multiple questions and return list of responses
        
        Args:
            questions: List of questions to ask
            
        Returns:
            List of question responses
        """
        responses = []
        
        for question in questions:
            response = self.ask_single_question(question)
            responses.append(response)
        
        return responses
    
    def search_documents(self, query: str, num_results: int = 5) -> Dict:
        """
        Search for documents matching query
        
        Args:
            query: Search query
            num_results: Number of results to return
            
        Returns:
            Dict with search results
        """
        try:
            results = self.qa_engine.search_documents(query, num_results)
            
            return {
                'success': True,
                'query': query,
                'num_results': len(results),
                'results': [
                    {
                        'title': result.document.title,
                        'score': result.score,
                        'content_preview': result.document.content[:200] + "...",
                        'highlights': result.highlights,
                        'file_path': result.document.file_path,
                        'file_type': result.document.file_type,
                        'chunk_index': result.document.chunk_index
                    }
                    for result in results
                ],
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'query': query,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_collection_info(self) -> Dict:
        """
        Get comprehensive collection information
        
        Returns:
            Dict with collection statistics
        """
        try:
            stats = self.qa_engine.get_collection_stats()
            
            return {
                'success': True,
                'collection_name': self.collection_name,
                'stats': stats,
                'conversation_history_count': len(self.qa_engine.conversation_history),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'collection_name': self.collection_name,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def export_conversation_history(self, output_file: str = None) -> Dict:
        """
        Export conversation history to file
        
        Args:
            output_file: Output file path (optional)
            
        Returns:
            Dict with export results
        """
        try:
            if output_file is None:
                output_file = f"conversation_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            self.qa_engine.export_conversation_history(output_file)
            
            return {
                'success': True,
                'output_file': output_file,
                'conversation_count': len(self.qa_engine.conversation_history),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def create_report(self, output_file: str = None) -> Dict:
        """
        Create comprehensive QA report
        
        Args:
            output_file: Output file path (optional)
            
        Returns:
            Dict with report results
        """
        try:
            if output_file is None:
                output_file = f"qa_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            # Gather all information
            collection_info = self.get_collection_info()
            
            report = {
                'report_metadata': {
                    'generated_at': datetime.now().isoformat(),
                    'collection_name': self.collection_name,
                    'report_type': 'QA Library Report'
                },
                'collection_info': collection_info,
                'conversation_history': [
                    {
                        'question': entry['question'],
                        'answer': entry['answer'],
                        'timestamp': entry['timestamp'].isoformat()
                    }
                    for entry in self.qa_engine.conversation_history
                ]
            }
            
            # Save report
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            return {
                'success': True,
                'output_file': output_file,
                'report_size': os.path.getsize(output_file),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# Convenience functions for quick tasks
def quick_import(documents_path: str, collection_name: str = "quick_collection") -> Dict:
    """Quick import of documents"""
    utility = QAUtility(collection_name)
    return utility.import_collection(documents_path)

def quick_ask(question: str, collection_name: str = "quick_collection") -> Dict:
    """Quick question asking"""
    utility = QAUtility(collection_name)
    return utility.ask_single_question(question)

def quick_search(query: str, collection_name: str = "quick_collection") -> Dict:
    """Quick document search"""
    utility = QAUtility(collection_name)
    return utility.search_documents(query)

def quick_batch_qa(questions: List[str], collection_name: str = "quick_collection") -> List[Dict]:
    """Quick batch question asking"""
    utility = QAUtility(collection_name)
    return utility.ask_multiple_questions(questions)

# Example usage
if __name__ == "__main__":
    print("🔧 QA Library Utilities - Task Examples")
    print("=" * 50)
    
    # Example 1: Import documents
    print("\n1. Import Documents")
    print("-" * 20)
    
    # Find some documents to import
    docx_files = list(Path('.').glob('*.docx'))
    if docx_files:
        result = quick_import(str(docx_files[0]), "utility_demo")
        print(f"Import result: {result}")
    
    # Example 2: Ask questions
    print("\n2. Ask Questions")
    print("-" * 20)
    
    questions = [
        "Tài liệu này nói về gì?",
        "Mục đích chính là gì?"
    ]
    
    responses = quick_batch_qa(questions, "utility_demo")
    for response in responses:
        if response['success']:
            print(f"Q: {response['question']}")
            print(f"A: {response['answer']}")
            print(f"Confidence: {response['confidence']:.2f}")
        else:
            print(f"Error: {response['error']}")
    
    # Example 3: Search documents
    print("\n3. Search Documents")
    print("-" * 20)
    
    search_result = quick_search("mục đích", "utility_demo")
    if search_result['success']:
        print(f"Found {search_result['num_results']} results")
        for result in search_result['results']:
            print(f"- {result['title']} (score: {result['score']:.2f})")
    
    print("\n✅ Utility examples completed!")
