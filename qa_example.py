#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
QA Library Example - Simple Task-Based Usage
"""

import os
import sys
from pathlib import Path
from qa import create_qa_engine, quick_qa, batch_qa

def demo_quick_setup():
    """Quick setup and basic usage"""
    print("🚀 Quick Setup Demo")
    print("=" * 30)
    
    # Create QA engine
    qa_engine = create_qa_engine("quick_demo")
    
    # Index a few documents if available
    docx_files = list(Path('.').glob('*.docx'))[:2]
    txt_files = list(Path('.').glob('*.txt'))[:2]
    
    indexed_count = 0
    
    # Index DOCX files
    for file_path in docx_files:
        print(f"📄 Indexing: {file_path}")
        if qa_engine.index_document_file(str(file_path)):
            indexed_count += 1
    
    # Index TXT files
    for file_path in txt_files:
        print(f"📄 Indexing: {file_path}")
        if qa_engine.index_document_file(str(file_path)):
            indexed_count += 1
    
    print(f"✅ Indexed {indexed_count} documents")
    return qa_engine

def demo_ask_questions(qa_engine):
    """Demonstrate question asking"""
    print("\n❓ Question Asking Demo")
    print("=" * 30)
    
    questions = [
        "Tài liệu này nói về gì?",
        "Mục đích chính của hoạt động là gì?",
        "Có những bước chuẩn bị nào?",
        "Tiến hành hoạt động như thế nào?",
        "Ngày thực hiện là khi nào?"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n❓ Question {i}: {question}")
        try:
            response = qa_engine.ask_question(question, num_context_docs=3)
            print(f"💬 Answer: {response.answer}")
            print(f"🎯 Confidence: {response.confidence:.2f}")
            print(f"📚 Sources: {len(response.sources)} documents")
            
            # Show source highlights
            if response.sources:
                print("📖 Top source:")
                source = response.sources[0]
                print(f"  • {source.document.title} (score: {source.score:.2f})")
                if source.highlights:
                    print(f"  • Highlight: {source.highlights[0]}")
        except Exception as e:
            print(f"❌ Error: {e}")

def demo_batch_processing():
    """Demonstrate batch processing"""
    print("\n⚡ Batch Processing Demo")
    print("=" * 30)
    
    batch_questions = [
        "Tóm tắt nội dung chính của tài liệu",
        "Liệt kê các bước thực hiện",
        "Ai là người thực hiện?",
        "Kết quả mong đợi là gì?"
    ]
    
    try:
        responses = batch_qa(batch_questions, collection_name="quick_demo")
        
        print(f"✅ Processed {len(responses)} questions")
        for i, response in enumerate(responses, 1):
            print(f"\n📝 Batch Result {i}:")
            print(f"  ❓ Q: {response.question}")
            print(f"  💬 A: {response.answer[:100]}...")
            print(f"  🎯 Confidence: {response.confidence:.2f}")
            
    except Exception as e:
        print(f"❌ Batch processing error: {e}")

def demo_search_functionality(qa_engine):
    """Demonstrate search functionality"""
    print("\n🔍 Search Demo")
    print("=" * 30)
    
    search_queries = [
        "mục đích hoạt động",
        "chuẩn bị thực hiện",
        "ngày tháng thời gian"
    ]
    
    for query in search_queries:
        print(f"\n🔍 Search: {query}")
        try:
            results = qa_engine.search_documents(query, num_results=3)
            
            if results:
                print(f"✅ Found {len(results)} results")
                for i, result in enumerate(results, 1):
                    print(f"  {i}. {result.document.title} (score: {result.score:.2f})")
            else:
                print("❌ No results found")
                
        except Exception as e:
            print(f"❌ Search error: {e}")

def demo_conversation_history(qa_engine):
    """Demonstrate conversation history"""
    print("\n💬 Conversation History Demo")
    print("=" * 30)
    
    conversation_questions = [
        "Tài liệu này về chủ đề gì?",
        "Bạn có thể nói rõ hơn về mục đích không?",
        "Còn về cách thực hiện thì sao?"
    ]
    
    for i, question in enumerate(conversation_questions, 1):
        print(f"\n💬 Turn {i}: {question}")
        try:
            response = qa_engine.ask_question(
                question, 
                use_conversation_history=True
            )
            print(f"🤖 Response: {response.answer}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"\n📚 Conversation entries: {len(qa_engine.conversation_history)}")

def demo_quick_qa():
    """Demonstrate quick QA function"""
    print("\n⚡ Quick QA Demo")
    print("=" * 30)
    
    try:
        # Quick QA without explicit setup
        answer = quick_qa("Tài liệu này nói về gì?", collection_name="quick_demo")
        print(f"💬 Quick Answer: {answer}")
        
    except Exception as e:
        print(f"❌ Quick QA error: {e}")

def main():
    """Main demo function with task selection"""
    print("🚀 QA Library - Task-Based Demo")
    print("=" * 50)
    
    # Quick setup
    qa_engine = demo_quick_setup()
    
    # Get collection stats
    stats = qa_engine.get_collection_stats()
    print(f"\n📊 Collection Stats: {stats}")
    
    # Run different demos
    try:
        # Demo 1: Basic Q&A
        demo_ask_questions(qa_engine)
        
        # Demo 2: Search functionality
        demo_search_functionality(qa_engine)
        
        # Demo 3: Conversation history
        demo_conversation_history(qa_engine)
        
        # Demo 4: Batch processing
        demo_batch_processing()
        
        # Demo 5: Quick QA
        demo_quick_qa()
        
        # Export conversation history
        print("\n💾 Exporting conversation history...")
        try:
            qa_engine.export_conversation_history("demo_conversation_history.json")
            print("✅ Conversation history exported")
        except Exception as e:
            print(f"❌ Export error: {e}")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    
    print("\n" + "=" * 50)
    print("✅ QA Library Demo Completed!")
    print("\nUsage Examples:")
    print("1. from qa import create_qa_engine")
    print("2. qa_engine = create_qa_engine('my_collection')")
    print("3. qa_engine.index_document_file('document.docx')")
    print("4. response = qa_engine.ask_question('Your question here')")
    print("5. print(response.answer)")
    print("\nFor advanced usage, check qa_example_structured.py!")

if __name__ == "__main__":
    main()
