#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Example usage of the new TypesenseVectorStore class
Demonstrates how to use the new structure following PHP implementation
"""

import os
import json
from typing import List
from libs.typesense_vector_db import (
    TypesenseVectorStore, 
    Document, 
    create_typesense_vector_store
)
from libs.custom_embedding import CustomEmbedding
from libs.common import get_env_var


def create_embedding_service() -> CustomEmbedding:
    """Create embedding service"""
    embeddings_model = get_env_var('EMBEDDINGS_MODEL', 'text-embedding-ada-002')
    embeddings_api_base = get_env_var('EMBEDDINGS_API_BASE')
    embeddings_api_key = get_env_var('EMBEDDINGS_API_KEY')
    
    return CustomEmbedding(
        model=embeddings_model,
        api_base=embeddings_api_base,
        api_key=embeddings_api_key
    )


def create_sample_documents(embedding_service: CustomEmbedding) -> List[Document]:
    """Create sample documents with embeddings"""
    
    sample_data = [
        {
            "content": "<PERSON><PERSON> thêm nhóm trẻ mới, vào menu Cấu hình > Nhóm trẻ, nhấn nút Thêm mới, điền thông tin và lưu.",
            "source_type": "manual",
            "source_name": "user_guide.txt",
            "hash": "doc_001",
            "chunk_number": 1
        },
        {
            "content": "Thay đổi giá dịch vụ: Vào Cấu hình > Dịch vụ, chọn dịch vụ cần sửa, cập nhật giá và nhấn Lưu.",
            "source_type": "manual", 
            "source_name": "user_guide.txt",
            "hash": "doc_002",
            "chunk_number": 2
        },
        {
            "content": "Cập nhật thực đơn: Truy cập Cấu hình > Thông tin thực đơn, nhập giá tiền mới cho bữa ăn và lưu thay đổi.",
            "source_type": "manual",
            "source_name": "user_guide.txt", 
            "hash": "doc_003",
            "chunk_number": 3
        },
        {
            "content": "Quản lý học sinh: Vào danh sách học sinh, có thể thêm mới, chỉnh sửa thông tin hoặc xóa học sinh.",
            "source_type": "manual",
            "source_name": "user_guide.txt",
            "hash": "doc_004", 
            "chunk_number": 4
        },
        {
            "content": "Báo cáo dinh dưỡng: Hệ thống tự động tính toán và tạo báo cáo dinh dưỡng dựa trên thực đơn đã thiết lập.",
            "source_type": "manual",
            "source_name": "user_guide.txt",
            "hash": "doc_005",
            "chunk_number": 5
        }
    ]
    
    documents = []
    
    print("🔄 Tạo documents với embeddings...")
    for i, data in enumerate(sample_data):
        print(f"   Đang xử lý document {i+1}/{len(sample_data)}")
        
        # Create document
        doc = Document(data["content"])
        doc.source_type = data["source_type"]
        doc.source_name = data["source_name"] 
        doc.hash = data["hash"]
        doc.chunk_number = data["chunk_number"]
        
        # Generate embedding
        doc.embedding = embedding_service.embed_query(data["content"])
        
        documents.append(doc)
    
    print(f"✅ Đã tạo {len(documents)} documents")
    return documents


def demonstrate_vector_store():
    """Demonstrate TypesenseVectorStore usage"""
    
    print("=" * 80)
    print(" DEMO: TypesenseVectorStore - New Implementation ")
    print("=" * 80)
    
    try:
        # 1. Create vector store
        print("\n1️⃣ Tạo TypesenseVectorStore...")
        vector_store = create_typesense_vector_store("demo_collection", 384)
        print("✅ Vector store created successfully")
        
        # 2. Create embedding service
        print("\n2️⃣ Tạo embedding service...")
        embedding_service = create_embedding_service()
        print("✅ Embedding service created successfully")
        
        # 3. Create sample documents
        print("\n3️⃣ Tạo sample documents...")
        documents = create_sample_documents(embedding_service)
        
        # 4. Add documents to vector store
        print("\n4️⃣ Thêm documents vào vector store...")
        vector_store.add_documents(documents, number_of_documents_per_request=2)
        print("✅ Documents added successfully")
        
        # 5. Test similarity search
        print("\n5️⃣ Test similarity search...")
        test_queries = [
            "cách thêm nhóm trẻ mới",
            "thay đổi giá dịch vụ", 
            "cập nhật thực đơn bữa ăn",
            "quản lý thông tin học sinh",
            "tạo báo cáo dinh dưỡng"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Query: '{query}'")
            
            # Generate query embedding
            query_embedding = embedding_service.embed_query(query)
            
            # Search similar documents
            results = vector_store.similarity_search(
                embedding=query_embedding,
                k=2,
                additional_arguments={}
            )
            
            print(f"📊 Found {len(results)} similar documents:")
            for i, doc in enumerate(results):
                print(f"   {i+1}. [{doc.hash}] {doc.content[:60]}...")
                print(f"      Source: {doc.source_name} (Type: {doc.source_type})")
        
        print("\n✅ Demo completed successfully!")
        
        # 6. Show summary
        print("\n📋 Summary:")
        print("- Created TypesenseVectorStore with 384-dimensional vectors")
        print(f"- Added {len(documents)} documents with embeddings")
        print(f"- Tested {len(test_queries)} similarity searches")
        print("- All operations completed without errors")
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()


def compare_with_legacy():
    """Show comparison between new and legacy approaches"""
    
    print("\n" + "=" * 80)
    print(" COMPARISON: New vs Legacy Implementation ")
    print("=" * 80)
    
    print("\n🆕 NEW TypesenseVectorStore:")
    print("✅ Clean Document objects with proper typing")
    print("✅ Interface-based design (VectorStoreInterface)")
    print("✅ Vector dimension validation")
    print("✅ Batch processing with NDJSON")
    print("✅ Proper error handling and exceptions")
    print("✅ Field names matching PHP implementation")
    print("✅ Factory function for easy setup")
    print("✅ Separation of concerns (embedding service separate)")
    
    print("\n🔄 LEGACY TypesenseVectorDB:")
    print("📝 Monolithic class with many responsibilities")
    print("📝 Mixed concerns (embedding, search, file processing)")
    print("📝 Less type safety")
    print("📝 More complex initialization")
    print("📝 Harder to test individual components")
    
    print("\n🎯 MIGRATION PATH:")
    print("1. Use new TypesenseVectorStore for new features")
    print("2. Gradually migrate existing code")
    print("3. Keep legacy class for backward compatibility")
    print("4. Eventually deprecate legacy implementation")


if __name__ == "__main__":
    print("🚀 Starting TypesenseVectorStore Demo...")
    
    # Run the main demonstration
    demonstrate_vector_store()
    
    # Show comparison
    compare_with_legacy()
    
    print("\n🎉 Demo finished!")
    print("\nNext steps:")
    print("- Try modifying the sample documents")
    print("- Experiment with different vector dimensions")
    print("- Test with your own embedding service")
    print("- Integrate into your existing applications")
