# QA Library with Typesense Integration

Thư viện hỏi đáp thông minh sử dụng Typesense để tìm kiếm và truy xuất thông tin từ tài liệu.

## Tính năng

- **Tìm kiếm thông minh**: Sử dụng Typesense search engine để tìm kiếm nhanh và chính xác
- **Xử lý nhiều định dạng**: Hỗ trợ file Word (.docx) và text (.txt)
- **Chunking thông minh**: Tự động chia nhỏ tài liệu để tối ưu hóa tìm kiếm
- **LLM tích hợp**: Tích hợp với nhiều LLM models để sinh câu trả lời
- **Lịch sử hội thoại**: <PERSON><PERSON> nhớ ngữ cảnh cuộc hội thoại
- **Xử lý batch**: Hỗ trợ xử lý nhiều câu hỏi cùng lúc
- **Th<PERSON><PERSON> kê và báo cáo**: <PERSON> dõi hiệu suất và độ tin cậy

## Cài đặt

### 1. Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### 2. Cài đặt và chạy Typesense

#### Sử dụng Docker:

```bash
docker run -d --name typesense -p 8108:8108 -v typesense-data:/data typesense/typesense:0.25.1 \
  --data-dir /data --api-key=xyz --enable-cors
```

#### Hoặc tải về binary:

```bash
# Tải Typesense cho macOS
curl -O https://dl.typesense.org/releases/0.25.1/typesense-server-0.25.1-darwin-amd64.tar.gz
tar -xzf typesense-server-0.25.1-darwin-amd64.tar.gz

# Chạy server
./typesense-server --data-dir=/tmp/typesense-data --api-key=xyz --enable-cors
```

### 3. Cấu hình môi trường

Thêm vào file `.env`:

```env
# Typesense Configuration
TYPESENSE_HOST=localhost
TYPESENSE_PORT=8108
TYPESENSE_API_KEY=xyz
TYPESENSE_PROTOCOL=http
TYPESENSE_DOCUMENTS_COLLECTION=documents

# Embeddings Configuration (for semantic chunking)
EMBEDDINGS_MODEL=text-embedding-3-small
EMBEDDINGS_API_BASE=https://api.openai.com/v1
EMBEDDINGS_API_KEY=your-openai-key
```

## Sử dụng cơ bản

### 1. Tạo QA Engine

```python
from qa import create_qa_engine

# Tạo QA engine với collection mặc định
qa_engine = create_qa_engine()

# Hoặc tạo với collection tùy chỉnh
qa_engine = create_qa_engine("my_collection")
```

### 2. Đánh index tài liệu

```python
# Index một file
qa_engine.index_document_file("path/to/document.docx")

# Index tất cả file trong thư mục
qa_engine.index_directory("path/to/documents/")

# Index với các extension cụ thể
qa_engine.index_directory("path/to/documents/", ['.docx', '.txt'])
```

### 3. Hỏi đáp

```python
# Hỏi một câu hỏi
response = qa_engine.ask_question("Tài liệu này nói về gì?")
print(f"Câu trả lời: {response.answer}")
print(f"Độ tin cậy: {response.confidence}")
print(f"Nguồn: {len(response.sources)} tài liệu")

# Hiển thị chi tiết nguồn
for source in response.sources:
    print(f"- {source.document.title} (điểm: {source.score})")
    if source.highlights:
        print(f"  Highlight: {source.highlights[0]}")
```

### 4. Hỏi đáp nhanh

```python
from qa import quick_qa

# Hỏi nhanh một câu hỏi
answer = quick_qa("Mục đích chính của hoạt động là gì?", "documents/")
print(answer)
```

### 5. Xử lý batch

```python
from qa import batch_qa

questions = [
    "Tài liệu này nói về gì?",
    "Các bước thực hiện là gì?",
    "Khi nào thực hiện?"
]

responses = batch_qa(questions, "documents/")
for response in responses:
    print(f"Q: {response.question}")
    print(f"A: {response.answer}")
    print()
```

## Tính năng nâng cao

### 1. Quản lý lịch sử hội thoại

```python
# Hỏi với ngữ cảnh lịch sử
response = qa_engine.ask_question("Còn gì khác nữa?", use_conversation_history=True)

# Xóa lịch sử
qa_engine.clear_conversation_history()

# Xuất lịch sử
qa_engine.export_conversation_history("history.json")
```

### 2. Tìm kiếm tài liệu

```python
# Tìm kiếm trực tiếp
search_results = qa_engine.search_documents("keyword", num_results=10)

for result in search_results:
    print(f"Title: {result.document.title}")
    print(f"Content: {result.document.content[:200]}...")
    print(f"Score: {result.score}")
```

### 3. Thống kê collection

```python
stats = qa_engine.get_collection_stats()
print(f"Collection: {stats.get('name')}")
print(f"Documents: {stats.get('num_documents')}")
print(f"Created: {stats.get('created_at')}")
```

### 4. Cấu hình tùy chỉnh

```python
from qa import QAEngine, TypesenseClient, DocumentProcessor

# Tạo custom document processor
processor = DocumentProcessor(chunk_size=1500, chunk_overlap=300)

# Tạo custom Typesense client
client = TypesenseClient(
    host="custom-host",
    port="8108",
    api_key="custom-key"
)

# Tạo QA engine với cấu hình tùy chỉnh
qa_engine = QAEngine("custom_collection")
qa_engine.typesense_client = client
qa_engine.document_processor = processor
```

## Cấu trúc dữ liệu

### Document

```python
@dataclass
class Document:
    id: str                    # ID duy nhất
    title: str                 # Tiêu đề
    content: str              # Nội dung
    file_path: str            # Đường dẫn file
    file_type: str            # Loại file (docx, txt)
    created_at: datetime      # Ngày tạo
    updated_at: datetime      # Ngày cập nhật
    chunk_index: int          # Chỉ số chunk
    total_chunks: int         # Tổng số chunk
    metadata: Dict[str, Any]  # Metadata
```

### QAResponse

```python
@dataclass
class QAResponse:
    question: str             # Câu hỏi
    answer: str              # Câu trả lời
    sources: List[SearchResult] # Nguồn tài liệu
    model_used: str          # Model LLM đã sử dụng
    tokens_used: int         # Số token đã sử dụng
    confidence: float        # Độ tin cậy (0-1)
    timestamp: datetime      # Thời gian
```

## Ví dụ hoàn chỉnh

```python
from qa import create_qa_engine

# Tạo QA engine
qa_engine = create_qa_engine("legal_docs")

# Index tài liệu
qa_engine.index_directory("legal_documents/")

# Hỏi đáp
while True:
    question = input("Hỏi (hoặc 'quit' để thoát): ")
    if question.lower() == 'quit':
        break
    
    response = qa_engine.ask_question(question)
    print(f"\n📝 Câu trả lời: {response.answer}")
    print(f"🎯 Độ tin cậy: {response.confidence:.2f}")
    print(f"📚 Nguồn: {len(response.sources)} tài liệu")
    
    if response.sources:
        print("\n📖 Chi tiết nguồn:")
        for i, source in enumerate(response.sources[:3]):
            print(f"  {i+1}. {source.document.title}")
            if source.highlights:
                print(f"     > {source.highlights[0]}")
    print("-" * 50)
```

## Troubleshooting

### 1. Lỗi kết nối Typesense

```bash
# Kiểm tra Typesense có chạy không
curl http://localhost:8108/health

# Kiểm tra API key
curl -H "X-TYPESENSE-API-KEY: xyz" http://localhost:8108/collections
```

### 2. Lỗi embedding

- Kiểm tra API key OpenAI
- Kiểm tra kết nối mạng
- Kiểm tra quota API

### 3. Lỗi processing documents

- Kiểm tra file có tồn tại không
- Kiểm tra quyền đọc file
- Kiểm tra định dạng file

## Hiệu suất

### Tối ưu hóa

1. **Chunk size**: Điều chỉnh `chunk_size` phù hợp với nội dung
2. **Embedding cache**: Sử dụng cache để tăng tốc
3. **Batch processing**: Xử lý nhiều tài liệu cùng lúc
4. **Index optimization**: Tối ưu hóa Typesense schema

### Monitoring

```python
# Thống kê hiệu suất
stats = qa_engine.get_collection_stats()
print(f"Total documents: {stats.get('num_documents')}")
print(f"Memory usage: {stats.get('memory_usage')}")
```

## Tích hợp với hệ thống khác

### FastAPI

```python
from fastapi import FastAPI
from qa import create_qa_engine

app = FastAPI()
qa_engine = create_qa_engine()

@app.post("/ask")
async def ask_question(question: str):
    response = qa_engine.ask_question(question)
    return {
        "question": response.question,
        "answer": response.answer,
        "confidence": response.confidence,
        "sources": len(response.sources)
    }
```

### Telegram Bot

```python
from qa import create_qa_engine

qa_engine = create_qa_engine()

def handle_message(message):
    response = qa_engine.ask_question(message.text)
    bot.send_message(message.chat.id, response.answer)
```

## Đóng góp

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## License

MIT License - xem file LICENSE để biết thêm chi tiết.
