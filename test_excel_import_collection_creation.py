#!/usr/bin/env python3
"""
Test script để kiểm tra chức năng tạo collection mới trong TypeSense
khi import Excel với import_excel_to_typesense
"""

import os
import sys
import pandas as pd
from datetime import datetime

# Add libs to path
sys.path.append('libs')

def create_test_excel_file():
    """Tạo file Excel test với dữ liệu mẫu"""
    test_data = {
        'Chức năng': ['Đăng nhập', 'Đăng ký', 'Quên mật khẩu', 'Thay đổi thông tin'],
        'Câu hỏi': [
            'Làm thế nào để đăng nhập vào hệ thống?',
            'Tôi muốn tạo tài khoản mới, làm sao?',
            'Tôi quên mật khẩu, phải làm gì?',
            'Làm sao để thay đổi thông tin cá nhân?'
        ],
        'Đ<PERSON><PERSON> án': [
            '<PERSON><PERSON><PERSON> có thể đăng nhập bằng cách nhập email và mật khẩu vào form đăng nhập.',
            '<PERSON><PERSON> tạo tài khoản mới, bạn click vào nút "Đăng ký" và điền đầy đủ thông tin.',
            'Nếu quên mật khẩu, bạn click vào "Quên mật khẩu" và làm theo hướng dẫn.',
            'Để thay đổi thông tin, bạn vào phần "Hồ sơ cá nhân" và chỉnh sửa.'
        ]
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_qa_data.xlsx'
    df.to_excel(test_file, index=False)
    print(f"✅ Đã tạo file test: {test_file}")
    return test_file

def test_import_with_new_collection():
    """Test import Excel với collection mới"""
    try:
        from typesense_vector_db import TypesenseVectorDB
        from collection_model import Collection
        
        # Tạo file Excel test
        test_file = create_test_excel_file()
        
        # Tạo collection mới trong MySQL trước
        collection_model = Collection()
        test_collection_name = f"test_collection_{int(datetime.now().timestamp())}"
        
        print(f"🔧 Tạo collection '{test_collection_name}' trong MySQL...")
        success = collection_model.create(
            test_collection_name, 
            f"Test Collection {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", 
            "Collection test cho việc import Excel"
        )
        
        if not success:
            print("❌ Không thể tạo collection trong MySQL")
            return False
        
        print(f"✅ Đã tạo collection '{test_collection_name}' trong MySQL")
        
        # Khởi tạo TypesenseVectorDB với collection mặc định
        typesense_db = TypesenseVectorDB("default_documents")
        
        # Test import với collection mới
        print(f"\n🚀 Bắt đầu import Excel vào collection '{test_collection_name}'...")
        result = typesense_db.import_excel_to_typesense(
            file_path=test_file,
            title="Test Q&A Data",
            metadata={"test": True, "created_by": "test_script"},
            collection_name=test_collection_name
        )
        
        print(f"\n📊 Kết quả import:")
        print(f"   - Success: {result.get('success', False)}")
        print(f"   - Total rows: {result.get('total_rows', 0)}")
        print(f"   - Imported documents: {result.get('imported_documents', 0)}")
        print(f"   - Skipped duplicates: {result.get('skipped_duplicates', 0)}")
        print(f"   - Collection name: {result.get('collection_name', 'N/A')}")
        print(f"   - Collection verified: {result.get('collection_verified', False)}")
        print(f"   - Collection created: {result.get('collection_created', False)}")
        
        if result.get('success'):
            print("✅ Test thành công!")
            
            # Test search trong collection mới
            print(f"\n🔍 Test search trong collection '{test_collection_name}'...")
            search_result = typesense_db.search_similar_documents(
                "làm sao đăng nhập", 
                limit=3, 
                collection_name=test_collection_name
            )
            
            if search_result.get('success'):
                print(f"✅ Search thành công, tìm thấy {search_result.get('total_found', 0)} documents")
                for i, doc in enumerate(search_result.get('documents', [])[:2]):
                    print(f"   {i+1}. {doc.get('title', 'N/A')} (similarity: {doc.get('similarity', 0):.3f})")
            else:
                print(f"❌ Search failed: {search_result.get('error', 'Unknown error')}")
        else:
            print(f"❌ Test failed: {result.get('error', 'Unknown error')}")
            return False
        
        # Cleanup
        try:
            os.remove(test_file)
            print(f"🧹 Đã xóa file test: {test_file}")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_with_default_collection():
    """Test import Excel với collection mặc định"""
    try:
        from typesense_vector_db import TypesenseVectorDB
        
        # Tạo file Excel test
        test_file = create_test_excel_file()
        
        # Khởi tạo TypesenseVectorDB với collection mặc định
        typesense_db = TypesenseVectorDB("default_test_documents")
        
        # Test import với collection mặc định (không cung cấp collection_name)
        print(f"\n🚀 Bắt đầu import Excel vào collection mặc định...")
        result = typesense_db.import_excel_to_typesense(
            file_path=test_file,
            title="Test Q&A Data - Default Collection",
            metadata={"test": True, "created_by": "test_script", "type": "default"}
        )
        
        print(f"\n📊 Kết quả import (default collection):")
        print(f"   - Success: {result.get('success', False)}")
        print(f"   - Total rows: {result.get('total_rows', 0)}")
        print(f"   - Imported documents: {result.get('imported_documents', 0)}")
        print(f"   - Collection name: {result.get('collection_name', 'N/A')}")
        print(f"   - Collection created: {result.get('collection_created', False)}")
        
        # Cleanup
        try:
            os.remove(test_file)
            print(f"🧹 Đã xóa file test: {test_file}")
        except:
            pass
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("TEST EXCEL IMPORT WITH COLLECTION CREATION")
    print("=" * 60)
    
    print("\n1. Testing import with new collection...")
    test1_success = test_import_with_new_collection()
    
    print("\n" + "=" * 60)
    print("\n2. Testing import with default collection...")
    test2_success = test_import_with_default_collection()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"   - Test 1 (New Collection): {'✅ PASSED' if test1_success else '❌ FAILED'}")
    print(f"   - Test 2 (Default Collection): {'✅ PASSED' if test2_success else '❌ FAILED'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)
