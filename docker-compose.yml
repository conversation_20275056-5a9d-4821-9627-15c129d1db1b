version: '3.8'

services:
  python3.12:
    build: .
    container_name: python3.12
    working_dir: /app
    volumes:
      - .:/app
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    ports:
      - 8001:8000
    stdin_open: true
    tty: true
    environment:
      - PYTHONUNBUFFERED=1
    #command: python3 main.py
    #command: bash
    command: "uvicorn main:app --host 0.0.0.0 --port 8000"
    #command: bash
    #build: .

