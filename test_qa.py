#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for QA library
"""

import os
import sys
import tempfile
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

def test_imports():
    """Test if all imports work correctly"""
    print("Testing imports...")
    
    try:
        from qa import (
            create_qa_engine, 
            quick_qa, 
            batch_qa,
            QAEngine,
            TypesenseClient,
            DocumentProcessor,
            Document,
            QAResponse,
            SearchResult
        )
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_environment():
    """Test environment configuration"""
    print("\nTesting environment configuration...")
    
    from libs.common import get_env_var
    
    # Check required environment variables
    required_vars = [
        'TYPESENSE_HOST',
        'TYPESENSE_PORT', 
        'TYPESENSE_API_KEY',
        'EMBEDDINGS_MODEL',
        'EMBEDDINGS_API_BASE',
        'EMBEDDINGS_API_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = get_env_var(var)
        if value is None:
            missing_vars.append(var)
        else:
            print(f"  ✅ {var}: {value}")
    
    if missing_vars:
        print(f"  ⚠️  Missing environment variables: {missing_vars}")
        print("  Please check your .env file")
        return False
    
    return True

def test_typesense_connection():
    """Test Typesense connection"""
    print("\nTesting Typesense connection...")
    
    try:
        from qa import TypesenseClient
        
        client = TypesenseClient()
        
        # Try to get health status
        import requests
        response = requests.get(f"{client.base_url}/health")
        
        if response.status_code == 200:
            print("✅ Typesense server is running")
            return True
        else:
            print(f"❌ Typesense server returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Typesense connection failed: {e}")
        print("  Please make sure Typesense server is running")
        return False

def test_document_processor():
    """Test document processing"""
    print("\nTesting document processor...")
    
    try:
        from qa import DocumentProcessor
        
        processor = DocumentProcessor()
        
        # Create a test text file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("This is a test document.\n")
            f.write("It contains multiple lines.\n")
            f.write("We will use it to test the document processor.\n")
            f.write("The processor should split this into chunks.")
            test_file = f.name
        
        # Process the test file
        documents = processor.process_text_file(test_file)
        
        if documents:
            print(f"✅ Successfully processed test file into {len(documents)} chunks")
            for i, doc in enumerate(documents):
                print(f"  - Chunk {i+1}: {len(doc.content)} characters")
        else:
            print("❌ No documents returned from processor")
            return False
        
        # Clean up
        os.unlink(test_file)
        
        return True
        
    except Exception as e:
        print(f"❌ Document processor test failed: {e}")
        return False

def test_qa_engine_creation():
    """Test QA engine creation"""
    print("\nTesting QA engine creation...")
    
    try:
        from qa import create_qa_engine
        
        qa_engine = create_qa_engine("test_collection")
        
        if qa_engine:
            print("✅ QA engine created successfully")
            print(f"  - Collection: {qa_engine.collection_name}")
            print(f"  - Typesense client: {type(qa_engine.typesense_client).__name__}")
            print(f"  - Document processor: {type(qa_engine.document_processor).__name__}")
            return True
        else:
            print("❌ Failed to create QA engine")
            return False
            
    except Exception as e:
        print(f"❌ QA engine creation failed: {e}")
        return False

def test_collection_operations():
    """Test collection operations"""
    print("\nTesting collection operations...")
    
    try:
        from qa import create_qa_engine
        
        qa_engine = create_qa_engine("test_collection_ops")
        
        # Test collection creation
        success = qa_engine.typesense_client.create_collection("test_collection_ops")
        if success:
            print("✅ Collection created successfully")
        else:
            print("❌ Failed to create collection")
            return False
        
        # Test collection stats
        stats = qa_engine.get_collection_stats()
        if stats:
            print(f"✅ Collection stats retrieved: {stats.get('name', 'Unknown')}")
        else:
            print("⚠️  No collection stats available")
        
        return True
        
    except Exception as e:
        print(f"❌ Collection operations failed: {e}")
        return False

def test_full_workflow():
    """Test complete workflow with sample data"""
    print("\nTesting full workflow...")
    
    try:
        from qa import create_qa_engine
        
        qa_engine = create_qa_engine("test_workflow")
        
        # Create test document
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Hoạt động học tập\n")
            f.write("Ngày thực hiện: 15-12-2024\n")
            f.write("Mục đích: Nâng cao kiến thức về trí tuệ nhân tạo\n")
            f.write("Chuẩn bị: Máy tính, tài liệu, kết nối internet\n")
            f.write("Tiến hành: Nghiên cứu các thuật toán machine learning\n")
            test_file = f.name
        
        # Index the document
        print("  - Indexing test document...")
        success = qa_engine.index_document_file(test_file)
        
        if not success:
            print("❌ Failed to index document")
            return False
        
        print("  ✅ Document indexed successfully")
        
        # Ask a question
        print("  - Asking test question...")
        response = qa_engine.ask_question("Mục đích của hoạt động là gì?")
        
        if response and response.answer:
            print(f"  ✅ Got answer: {response.answer[:100]}...")
            print(f"  - Confidence: {response.confidence:.2f}")
            print(f"  - Sources: {len(response.sources)}")
        else:
            print("❌ No answer received")
            return False
        
        # Clean up
        os.unlink(test_file)
        
        return True
        
    except Exception as e:
        print(f"❌ Full workflow test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 QA Library Test Suite")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Environment", test_environment),
        ("Typesense Connection", test_typesense_connection),
        ("Document Processor", test_document_processor),
        ("QA Engine Creation", test_qa_engine_creation),
        ("Collection Operations", test_collection_operations),
        ("Full Workflow", test_full_workflow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! QA library is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the configuration.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
