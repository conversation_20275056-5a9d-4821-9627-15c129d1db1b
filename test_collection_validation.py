#!/usr/bin/env python3
"""
Unit Tests: Collection Validation

Tá<PERSON>: AI Assistant
<PERSON><PERSON><PERSON>: 2025-07-07

Test các chức năng:
1. Collection Model CRUD operations
2. Excel import với collection validation
3. Database integration
"""

import unittest
import os
import sys
import tempfile
from pathlib import Path

# Thêm thư mục libs vào Python path
sys.path.append(str(Path(__file__).parent / "libs"))

from libs.database_manager import DatabaseManager
from libs.collection_model import Collection
from libs.typesense_vector_db import TypesenseVectorDB
import pandas as pd


class TestCollectionModel(unittest.TestCase):
    """Test Collection Model"""
    
    def setUp(self):
        """Khởi tạo trước mỗi test"""
        self.collection_model = Collection()
        self.test_collection_name = "test_collection_unit"
        
        # Xóa collection test nếu tồn tại
        if self.collection_model.exists(self.test_collection_name):
            self.collection_model.delete(self.test_collection_name, soft_delete=False)
    
    def tearDown(self):
        """Dọn dẹp sau mỗi test"""
        # Xóa collection test
        if self.collection_model.exists(self.test_collection_name):
            self.collection_model.delete(self.test_collection_name, soft_delete=False)
    
    def test_create_collection(self):
        """Test tạo collection"""
        result = self.collection_model.create(
            self.test_collection_name,
            "Test Collection",
            "Test collection for unit testing"
        )
        self.assertTrue(result, "Tạo collection thất bại")

        # Kiểm tra collection đã được tạo
        self.assertTrue(
            self.collection_model.exists(self.test_collection_name),
            "Collection không tồn tại sau khi tạo"
        )
    
    def test_get_collection_by_name(self):
        """Test lấy collection theo tên"""
        # Tạo collection trước
        self.collection_model.create(
            self.test_collection_name,
            "Test Collection",
            "Test collection"
        )

        # Lấy collection
        collection = self.collection_model.get_by_name(self.test_collection_name)
        self.assertIsNotNone(collection, "Không lấy được collection")
        self.assertEqual(collection['name'], self.test_collection_name)
        self.assertEqual(collection['display_name'], "Test Collection")
        self.assertEqual(collection['description'], "Test collection")
    
    def test_collection_exists(self):
        """Test kiểm tra collection tồn tại"""
        # Collection chưa tồn tại
        self.assertFalse(
            self.collection_model.exists(self.test_collection_name),
            "Collection không nên tồn tại"
        )
        
        # Tạo collection
        self.collection_model.create(self.test_collection_name, "Test Collection", "Test")
        
        # Collection đã tồn tại
        self.assertTrue(
            self.collection_model.exists(self.test_collection_name),
            "Collection nên tồn tại"
        )
    
    def test_update_collection(self):
        """Test cập nhật collection"""
        # Tạo collection
        self.collection_model.create(self.test_collection_name, "Original Collection", "Original description")

        # Cập nhật description và display_name
        result = self.collection_model.update(
            self.test_collection_name,
            display_name="Updated Collection",
            description="Updated description"
        )
        self.assertTrue(result, "Cập nhật collection thất bại")

        # Kiểm tra đã cập nhật
        collection = self.collection_model.get_by_name(self.test_collection_name)
        self.assertEqual(collection['display_name'], "Updated Collection")
        self.assertEqual(collection['description'], "Updated description")
    
    def test_delete_collection_soft(self):
        """Test xóa mềm collection"""
        # Tạo collection
        self.collection_model.create(self.test_collection_name, "Test Collection", "Test")

        # Xóa mềm
        result = self.collection_model.delete(self.test_collection_name, soft_delete=True)
        self.assertTrue(result, "Xóa mềm collection thất bại")

        # Collection không còn exists (vì is_active=False)
        self.assertFalse(
            self.collection_model.exists(self.test_collection_name),
            "Collection vẫn tồn tại sau khi xóa mềm"
        )
    
    def test_get_all_collections(self):
        """Test lấy tất cả collections"""
        # Tạo một vài collections
        test_collections = [
            f"{self.test_collection_name}_1",
            f"{self.test_collection_name}_2"
        ]
        
        for name in test_collections:
            self.collection_model.create(name, f"Test Collection {name}", f"Test collection {name}")
        
        # Lấy tất cả collections
        collections = self.collection_model.get_all()
        collection_names = [c['name'] for c in collections]
        
        # Kiểm tra collections đã tạo có trong danh sách
        for name in test_collections:
            self.assertIn(name, collection_names, f"Collection {name} không có trong danh sách")
        
        # Dọn dẹp
        for name in test_collections:
            self.collection_model.delete(name, soft_delete=False)


class TestExcelImportValidation(unittest.TestCase):
    """Test Excel Import với Collection Validation"""
    
    def setUp(self):
        """Khởi tạo trước mỗi test"""
        self.collection_model = Collection()
        self.test_collection_name = "test_excel_collection"
        self.excel_file = None
        
        # Tạo collection test
        if not self.collection_model.exists(self.test_collection_name):
            self.collection_model.create(
                self.test_collection_name,
                "Test Excel Collection",
                "Test collection for Excel import"
            )
        
        # Tạo file Excel tạm
        self.excel_file = self._create_temp_excel_file()
    
    def tearDown(self):
        """Dọn dẹp sau mỗi test"""
        # Xóa collection test
        if self.collection_model.exists(self.test_collection_name):
            self.collection_model.delete(self.test_collection_name, soft_delete=False)
        
        # Xóa file Excel tạm
        if self.excel_file and os.path.exists(self.excel_file):
            os.remove(self.excel_file)
    
    def _create_temp_excel_file(self):
        """Tạo file Excel tạm cho test"""
        data = {
            'Chức năng': ['Test', 'Test'],
            'Câu hỏi': ['Question 1?', 'Question 2?'],
            'Đáp án': ['Answer 1', 'Answer 2']
        }
        
        df = pd.DataFrame(data)
        
        # Tạo file tạm
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp:
            df.to_excel(tmp.name, index=False)
            return tmp.name
    
    def test_import_with_valid_collection(self):
        """Test import với collection hợp lệ"""
        try:
            db = TypesenseVectorDB(collection_name="test_typesense")
            result = db.import_excel_to_typesense(
                file_path=self.excel_file,
                title="Test Excel",
                collection_name=self.test_collection_name  # Collection hợp lệ
            )
            
            self.assertTrue(result["success"], f"Import thất bại: {result.get('error', 'Unknown error')}")
            self.assertTrue(result["collection_verified"], "Collection không được verify")
            self.assertEqual(result["collection_name"], self.test_collection_name)
            
        except Exception as e:
            self.skipTest(f"Không thể test do lỗi Typesense: {e}")
    
    def test_import_with_invalid_collection(self):
        """Test import với collection không hợp lệ"""
        try:
            db = TypesenseVectorDB(collection_name="test_typesense")
            result = db.import_excel_to_typesense(
                file_path=self.excel_file,
                title="Test Excel",
                collection_name="invalid_collection_name"  # Collection không tồn tại
            )
            
            self.assertFalse(result["success"], "Import không nên thành công với collection không hợp lệ")
            self.assertIn("không tồn tại", result["error"], "Thông báo lỗi không đúng")
            
        except Exception as e:
            self.skipTest(f"Không thể test do lỗi Typesense: {e}")
    
    def test_import_without_collection_validation(self):
        """Test import không có collection validation"""
        try:
            db = TypesenseVectorDB(collection_name="test_typesense")
            result = db.import_excel_to_typesense(
                file_path=self.excel_file,
                title="Test Excel"
                # Không có collection_name
            )
            
            self.assertTrue(result["success"], f"Import thất bại: {result.get('error', 'Unknown error')}")
            self.assertFalse(result["collection_verified"], "Collection không nên được verify")
            self.assertIsNone(result["collection_name"], "Collection name nên là None")
            
        except Exception as e:
            self.skipTest(f"Không thể test do lỗi Typesense: {e}")


class TestDatabaseIntegration(unittest.TestCase):
    """Test tích hợp Database"""
    
    def test_database_connection(self):
        """Test kết nối database"""
        db_manager = DatabaseManager()
        result = db_manager.connect()
        self.assertTrue(result, "Không thể kết nối database")
        
        if db_manager.connection:
            db_manager.disconnect()
    
    def test_create_tables(self):
        """Test tạo bảng"""
        db_manager = DatabaseManager()
        if db_manager.connect():
            result = db_manager.create_tables()
            self.assertTrue(result, "Không thể tạo bảng")
            db_manager.disconnect()
        else:
            self.skipTest("Không thể kết nối database")


def run_tests():
    """Chạy tất cả tests"""
    print("🧪 CHẠY UNIT TESTS - Collection Validation")
    print("=" * 60)
    
    # Tạo test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Thêm test cases
    suite.addTests(loader.loadTestsFromTestCase(TestCollectionModel))
    suite.addTests(loader.loadTestsFromTestCase(TestExcelImportValidation))
    suite.addTests(loader.loadTestsFromTestCase(TestDatabaseIntegration))
    
    # Chạy tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Tóm tắt kết quả
    print("\n" + "=" * 60)
    print(" KẾT QUẢ TESTS ")
    print("=" * 60)
    print(f"✅ Tests thành công: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ Tests thất bại: {len(result.failures)}")
    print(f"💥 Tests lỗi: {len(result.errors)}")
    print(f"⏭️ Tests bỏ qua: {len(result.skipped)}")
    
    if result.failures:
        print(f"\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback}")
    
    if result.errors:
        print(f"\n💥 ERRORS:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
